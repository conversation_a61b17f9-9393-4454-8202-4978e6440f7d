# ls-manage

本项目是一个基于 Angular 20+ 和 Material Design 的现代化前端管理后台应用。

## 技术栈

- **框架**: Angular (~20.1.3)
- **语言**: TypeScript (~5.8.3)
- **包管理器**: pnpm
- **UI 组件库**: Angular Material (~20.1.3)
- **响应式布局**: @ngbracket/ngx-layout
- **动态表单**: @ngx-formly/core
- **状态管理**: @ngxs/store
- **国际化**: @ngx-translate/core
- **权限控制**: ngx-permissions
- **样式**: SCSS, Material Symbols
- **工具**: RxJS, date-fns

## 项目结构

```
/home/<USER>/tidemo/ls-manage/
├───src/
│   ├───app/
│   │   ├───components/       # 可复用的UI组件
│   │   ├───layouts/          # 页面布局 (如：管理后台布局、登录页布局)
│   │   ├───pages/            # 页面级组件 (按路由划分)
│   │   ├───services/         # 业务服务和逻辑
│   │   ├───providers/        # 应用启动和配置服务
│   │   ├───pipes/            # 自定义管道
│   │   ├───directives/       # 自定义指令
│   │   └───envs/             # 环境变量
│   ├───assets/
│   │   └───i18n/             # 国际化语言包
│   └───styles/               # 全局样式、主题和辅助类
├───angular.json              # Angular CLI 配置文件
├───package.json              # 项目依赖和脚本
└───GEMINI.md                 # AI 辅助开发配置文件
```

## 可用脚本

你可以使用 `pnpm` 来运行这些脚本：

- `pnpm start`: 启动本地开发服务器，访问 `http://localhost:4200/`。
- `pnpm build`: 为开发环境构建项目。
- `pnpm build:prod`: 为生产环境构建项目。
- `pnpm test`: 运行 Karma 进行单元测试。
- `pnpm watch`: 在监视模式下构建项目，文件发生变化时自动重新构建。

## 编码规范和最佳实践

本项目遵循 `GEMINI.md` 中定义的编码规范。

### TypeScript 最佳实践
- 使用严格类型检查
- 当类型明显时，优先使用类型推断
- 避免使用 `any` 类型；当类型不确定时使用 `unknown`

### Angular 最佳实践
- 始终使用独立组件 (Standalone Components) 而不是 NgModules
- 不要在 `@Component`, `@Directive` 和 `@Pipe` 装饰器内部设置 `standalone: true`
- 使用信号 (Signals) 进行状态管理
- 为功能路由实现懒加载
- 所有静态图片使用 `NgOptimizedImage`
- 不要使用 `@HostBinding` 和 `@HostListener` 装饰器，应将宿主绑定放在 `@Component` 或 `@Directive` 装饰器的 `host` 对象中

### 组件 (Components)
- 保持组件小巧，并专注于单一职责
- 使用 `input()` 和 `output()` 函数代替装饰器
- 使用 `computed()` 处理派生状态
- 在 `@Component` 装饰器中设置 `changeDetection: ChangeDetectionStrategy.OnPush`
- 对于小型组件，优先使用内联模板
- 优先使用响应式表单 (Reactive Forms) 而不是模板驱动表单
- 不要使用 `ngClass`，使用 `class` 绑定代替
- 不要使用 `ngStyle`，使用 `style` 绑定代替

### 状态管理 (State Management)
- 使用信号管理本地组件状态
- 使用 `computed()` 处理派生状态
- 保持状态转换的纯粹和可预测性
- 不要在信号上使用 `mutate`，应使用 `update` 或 `set`

### 模板 (Templates)
- 保持模板简洁，避免复杂逻辑
- 使用原生控制流 (`@if`, `@for`, `@switch`) 代替 `*ngIf`, `*ngFor`, `*ngSwitch`
- 使用 `async` 管道处理可观察对象 (Observables)

### 服务 (Services)
- 围绕单一职责设计服务
- 对单例服务使用 `providedIn: 'root'` 选项
- 使用 `inject()` 函数代替构造函数注入
