import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { map } from 'rxjs';

import { Menu } from '@/services/bootstrap';
import { API_PATHS } from '@/utils/variable';
import { Token, User } from './interface';

@Injectable({
  providedIn: 'root',
})
export class LoginService {
  protected readonly http = inject(HttpClient);

  login(username: string, password: string, rememberMe = false) {
    return this.http.post<Token>(API_PATHS.LOGIN, { username, password, rememberMe });
  }

  refresh(params: Record<string, any>) {
    return this.http.post<Token>(API_PATHS.REFRESH, params);
  }

  logout() {
    return this.http.post<any>(API_PATHS.LOGOUT, {});
  }

  user(userId: number) {
    return this.http.get<User>(`${API_PATHS.USER}/${userId}`);
  }

  menu(roleId: number) {
    return this.http.get<{ data: Menu[] }>(`${API_PATHS.MENU}/role/${roleId}`).pipe(map(res => res.data));
  }
}
