import { Component } from '@angular/core';
import { FieldWrapper,FormlyField } from '@ngx-formly/core';
import { FlexLayoutModule } from '@ngbracket/ngx-layout';

@Component({
  selector: 'formly-wrapper-flexlayout',
  template: `
    <div
      class="formly-flex-wrapper"
      [fxLayout]="props['fxLayout']"
      [fxLayoutGap]="props['fxLayoutGap']"
      [fxLayoutAlign]="props['fxLayoutAlign']"
    >
      @for (fieldItem of field.fieldGroup; track fieldItem) {
        @if (fieldItem.props?.['flex'] || props['defaultFlex']; as flexConfig) {
          <div
            [fxFlex]="flexConfig['fxFlex']"
            [fxFlex.lg]="flexConfig['fxFlex.lg']"
            [fxFlex.md]="flexConfig['fxFlex.md']"
            [fxFlex.sm]="flexConfig['fxFlex.sm']"
            [fxFlex.xs]="flexConfig['fxFlex.xs']"
          >
            <formly-field [field]="fieldItem"></formly-field>
          </div>
        } @else {
          <div>
            <formly-field [field]="fieldItem"></formly-field>
          </div>
        }
      }
    </div>
  `,
  styles: [
    `
      .formly-flex-wrapper {
        display: flex;
      }
    `,
  ],
  imports: [FlexLayoutModule, FormlyField],
})
export class FlexLayoutWrapper extends FieldWrapper {
  get flexConfig() {
    return this.props['defaultFlex'] || {};
  }
}
