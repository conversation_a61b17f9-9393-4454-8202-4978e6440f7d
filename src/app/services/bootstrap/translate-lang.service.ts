import { Injectable, inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SettingsService } from './settings.service';

@Injectable({
  providedIn: 'root',
})
export class TranslateLangService {
  private readonly translate = inject(TranslateService);
  private readonly settings = inject(SettingsService);

  constructor() {
    // Listen for language changes from SettingsService
    this.settings.languageChange.subscribe(language => {
      if (language) {
        this.changeLanguage(language);
      }
    });
  }

  load() {
    return new Promise<void>(resolve => {
      // Add available languages to TranslateService
      const languages = this.settings.languages;
      this.translate.addLangs(languages);

      const defaultLang = this.settings.getTranslateLang();
      console.log(`Attempting to load language: ${defaultLang}`);

      // Set default language and use it
      // this.translate.setDefaultLang(defaultLang);
      this.translate.use(defaultLang).subscribe({
        next: translations => {
          console.log(`Successfully initialized '${defaultLang}' language.`, translations);
        },
        error: error => {
          console.error(`Problem with '${defaultLang}' language initialization.`, error);
        },
        complete: () => {
          console.log(`Language loading completed for: ${defaultLang}`);
          resolve();
        },
      });
    });
  }

  /**
   * Change the current language
   */
  changeLanguage(language: string) {
    console.log(`Changing language to: ${language}`);
    this.translate.use(language).subscribe({
      next: () => console.log(`Language changed to: ${language}`),
      error: (error) => console.error(`Error changing language to ${language}:`, error)
    });
  }
}
