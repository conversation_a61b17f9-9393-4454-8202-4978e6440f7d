import { Injectable, inject } from '@angular/core';
import { NgxPermissionsService, NgxRolesService } from 'ngx-permissions';
import { switchMap, tap } from 'rxjs';

import { LoginService, User } from '@/services/authentication';
import { Menu, MenuService } from './menu.service';

@Injectable({
  providedIn: 'root',
})
export class StartupService {
  // private readonly authService = inject(AuthService);
  private readonly loginService = inject(LoginService);
  private readonly menuService = inject(MenuService);
  private readonly permissonsService = inject(NgxPermissionsService);
  private readonly rolesService = inject(NgxRolesService);

  /**
   * Load the application only after get the menu or other essential informations
   * such as permissions and roles.
   */
  load() {
    return new Promise<void>((resolve, reject) => {
      // this.authService
      //   .change()
      //   .pipe(
      //     tap(user => this.setPermissions(user)),
      //     switchMap(() => this.authService.menu()),
      //     tap(menu => this.setMenu(menu))
      //   )
      //   .subscribe({
      //     next: () => resolve(),
      //     error: () => resolve(),
      //   });
      this.loginService.menu(100).pipe(
        tap(menu => this.setMenu(menu))
      ).subscribe({
        next: () => resolve(),
        error: () => resolve(),
      });
    });
  }

  private setMenu(menu: Menu[]) {
    console.log(menu);
    this.menuService.addNamespace(menu, 'menu');
    this.menuService.set(menu);
  }

  private setPermissions(user: User) {
    // In a real app, you should get permissions and roles from the user information.
    const permissions = ['canAdd', 'canDelete', 'canEdit', 'canRead'];
    this.permissonsService.loadPermissions(permissions);
    this.rolesService.flushRoles();
    this.rolesService.addRoles({ ADMIN: permissions });

    // Tips: Alternatively you can add permissions with role at the same time.
    // this.rolesService.addRolesWithPermissions({ ADMIN: permissions });
  }
}
