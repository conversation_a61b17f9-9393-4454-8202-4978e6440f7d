<app-page-header title="menu.system.role" />

<div class="container">
  @if (!formVisible()) {
  <div class="card">
    <div class="card-header">
      <button class="btn btn-primary" (click)="onAdd()">Add Role</button>
    </div>
    <div class="card-body">
      <table class="table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Role Name</th>
            <th>Role Code</th>
            <th>Sort</th>
            <th>State</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          @for (role of roles(); track role.roleId) {
          <tr>
            <td>{{ role.roleId }}</td>
            <td>{{ role.name }}</td>
            <td>{{ role.code }}</td>
            <td>{{ role.sort }}</td>
            <td>{{ role.state === '0' ? 'Active' : 'Inactive' }}</td>
            <td>
              <button class="btn btn-sm btn-secondary" (click)="onEdit(role)">Edit</button>
              <button class="btn btn-sm btn-danger" (click)="onDelete(role.roleId)">Delete</button>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
  } @else {
  <div class="card">
    <div class="card-header">
      <h2>{{ isEdit ? 'Edit Role' : 'Add Role' }}</h2>
    </div>
    <div class="card-body">
      <form [formGroup]="roleForm" (ngSubmit)="onSave()">
        <div class="form-group">
          <label for="name">Role Name</label>
          <input type="text" id="name" formControlName="name" class="form-control"
            [ngClass]="{ 'is-invalid': submitted && f['name'].errors }">
          @if (submitted && f['name'].errors) {
          <div class="invalid-feedback">
            @if (f['name'].errors['required']) {
            <div>Role Name is required</div>
            }
          </div>
          }
        </div>

        <div class="form-group">
          <label for="code">Role Code</label>
          <input type="text" id="code" formControlName="code" class="form-control"
            [ngClass]="{ 'is-invalid': submitted && f['code'].errors }">
          @if (submitted && f['code'].errors) {
          <div class="invalid-feedback">
            @if (f['code'].errors['required']) {
            <div>Role Code is required</div>
            }
          </div>
          }
        </div>

        <div class="form-group">
          <label for="sort">Sort</label>
          <input type="number" id="sort" formControlName="sort" class="form-control">
        </div>

        <div class="form-group">
          <label for="dataScope">Data Scope</label>
          <select id="dataScope" formControlName="dataScope" class="form-control">
            <option value="0">All</option>
            <option value="1">Custom</option>
            <option value="2">Department</option>
            <option value="3">Department and below</option>
          </select>
        </div>

        <div class="form-group">
          <label for="state">State</label>
          <select id="state" formControlName="state" class="form-control">
            <option value="0">Active</option>
            <option value="1">Inactive</option>
          </select>
        </div>

        <div class="form-group">
          <label for="remark">Remark</label>
          <textarea id="remark" formControlName="remark" class="form-control"></textarea>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn btn-primary">Save</button>
          <button type="button" class="btn btn-secondary" (click)="onCancel()">Cancel</button>
        </div>
      </form>
    </div>
  </div>
  }
</div>
