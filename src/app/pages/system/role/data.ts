import { FetchRespByPage, FetchResp } from '@/utils/entities';

export interface ROLE {
  roleId: number;
  code: string;
  name: string;
  sort: number;
  dataScope: '0' | '1' | '2' | '3';
  state: '0' | '1';
  isSystem: '0' | '1';
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  remark: string;
}

export type FetchRole = FetchResp & {
  data: ROLE | string | null;
}

export type FetchRoleByPage = FetchRespByPage & {
  list: ROLE[];
}
