import { ChangeDetectionStrategy, Component, OnInit, inject, signal } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

import { PageHeader } from '@/components/page-header';
import { ROLE } from './data';
import { RoleService } from './index.service';

@Component({
  selector: 'app-role',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, PageHeader],
  providers: [RoleService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Role implements OnInit {
  private roleService = inject(RoleService);
  private fb = inject(FormBuilder);

  roles = signal<ROLE[]>([]);
  roleForm!: FormGroup;
  isEdit = false;
  submitted = false;
  formVisible = signal(false);
  selectedRole = signal<ROLE | null>(null);

  ngOnInit(): void {
    this.loadRoles();
    this.initForm();
  }

  loadRoles(): void {
    this.roleService.getRoles(`page=1&limit=10&sortby=&order=`).subscribe(resp => {
      if (resp.count > 0) {
        this.roles.set(resp.list);
      }
    });
  }

  initForm(): void {
    this.roleForm = this.fb.group({
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      sort: [0],
      dataScope: ['0', [Validators.required]],
      state: ['0', [Validators.required]],
      remark: [''],
    });
  }

  get f() { return this.roleForm.controls; }

  onAdd(): void {
    this.isEdit = false;
    this.submitted = false;
    this.selectedRole.set(null);
    this.roleForm.reset({
      sort: 0,
      dataScope: '0',
      state: '0',
    });
    this.formVisible.set(true);
  }

  onEdit(role: ROLE): void {
    this.isEdit = true;
    this.submitted = false;
    this.selectedRole.set(role);
    this.roleForm.patchValue(role);
    this.formVisible.set(true);
  }

  onDelete(role_id: number): void {
    // In a real app, you'd probably want a confirmation dialog here.
    this.roleService.deleteRole(role_id).subscribe(() => {
      this.loadRoles();
    });
  }

  onSave(): void {
    this.submitted = true;
    if (this.roleForm.invalid) {
      return;
    }

    const formValue = this.roleForm.value;
    const roleData: Partial<ROLE> = {
      name: formValue.name,
      code: formValue.code,
      sort: formValue.sort,
      dataScope: formValue.dataScope,
      state: formValue.state,
      remark: formValue.remark,
    };

    if (this.isEdit && this.selectedRole()) {
      this.roleService.updateRole(this.selectedRole()!.roleId, roleData).subscribe(() => {
        this.loadRoles();
        this.onCancel();
      });
    } else {
      this.roleService.addRole(roleData).subscribe(() => {
        this.loadRoles();
        this.onCancel();
      });
    }
  }

  onCancel(): void {
    this.formVisible.set(false);
    this.selectedRole.set(null);
  }
}
