import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { API_PATHS } from '@/utils/variable';
import { ROLE, FetchRole, FetchRoleByPage } from './data';


@Injectable({
  providedIn: 'root'
})
export class RoleService {
  private http = inject(HttpClient);
  private apiUrl = API_PATHS.ROLE; // 假设的API端点

  /**
   * 分页查询
   * @param params `page=?&limit=?&sortby=?&order=?&name=?&key=?&state=?&createdAt=?`
   */
  getRoles(params: string): Observable<FetchRoleByPage> {
    return this.http.get<FetchRoleByPage>(`${this.apiUrl}?${params}`);
  }

  /**
   * 单个查询
   * @param id `roleId`
   */
  getRole(id: number): Observable<FetchRole> {
    return this.http.get<FetchRole>(`${this.apiUrl}/${id}`);
  }

  /** 新增 */
  addRole(role: Partial<ROLE>): Observable<FetchRole> {
    return this.http.post<FetchRole>(this.apiUrl, role);
  }

  /** 更新 */
  updateRole(id: number, role: Partial<ROLE>): Observable<FetchRole> {
    return this.http.put<FetchRole>(`${this.apiUrl}/${id}`, role);
  }

  /** 删除 */
  deleteRole(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
