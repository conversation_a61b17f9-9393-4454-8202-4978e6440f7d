import { Component } from '@angular/core';

import { QueryFilter } from '@/components/forms';
// import { BaseTable } from '@/components/tables';
import { USER, searchForm, TableColumns } from './data';

@Component({
  selector: 'app-user',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  imports: [
    QueryFilter,
    // BaseTable,
  ],
})
export class User {
  searchForm = searchForm;
  tableColumns = TableColumns;
  users: USER[] = [];
}
