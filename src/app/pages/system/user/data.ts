import { FormlyFieldConfig } from '@ngx-formly/core';

import { BaseInfo } from '@/utils/entities';

const defaultFormFlex = {
  'fxFlex.xs': '100%',
  'fxFlex.sm': '50%',
  'fxFlex.md': '33.33%',
  'fxFlex.lg': '25%',
  'fxFlex': '20%'
};

export interface USER extends BaseInfo {
  userId: number;
  deptId: number;
  userName: string;
  nickName: string;
  realName: string;
  userType: string;
  mobile: string;
  email: string;
  gender: string;
  avatar: string;
  state: string;
  loginCount: number;
  loginIp?: string;
  loginAt?: string;
  pwdUpdatedAt?: string;
  isSystem: string;
  remark: string;
  deptName?: string;
  postIds?: string;
  roleId?: string;
}

export const searchForm: FormlyFieldConfig[] = [{
  wrappers: ['flexlayout'],
  props: {
    fxLayout: 'row wrap',
    fxLayoutAlign: 'start center',
    fxLayoutGap: '16px grid',
    defaultFlex: defaultFormFlex
  },
  fieldGroup: [{
    key: 'loginName',
    type: 'input',
    props: {
      label: '用户名称',
      required: true,
    }
  }, {
    key: 'phone',
    type: 'input',
    props: {
      label: '手机号码',
    }
  }, {
    key: 'status',
    type: 'select',
    props: {
      label: '状态',
    }
  }, {
    key: 'exchangeid',
    type: 'input',
    props: {
      label: '创建时间',
    }
  }]
}];

export const TableColumns = [{
  header: '登录账号',
  field: 'username',
}, {
  header: '用户昵称',
  field: 'nickname',
}, {
  header: '部门',
  field: 'deptName',
}, {
  header: '性别',
  field: 'gender',
}, {
  header: '手机号码',
  field: 'mobile',
}, {
  header: '状态',
  field: 'status',
}, {
  header: '创建时间',
  field: 'createdAt',
}];
