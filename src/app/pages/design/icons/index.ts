import { KeyValuePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FlexLayoutModule } from '@ngbracket/ngx-layout';

import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';

import { MAT_ICONS } from './icons';

@Component({
  selector: 'app-design-icons',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  imports: [KeyValuePipe, MatCardModule, MatIconModule, FlexLayoutModule],
})
export class DesignIcons implements OnInit {
  icons!: Record<string, string[]>;

  ngOnInit() {
    this.icons = MAT_ICONS;
  }

  trackByIcon(index: number, icon: { key: string; value: string[] }): string {
    return icon.key;
  }

  iconClick(icon: string) {
    console.log(icon);
  }
}
