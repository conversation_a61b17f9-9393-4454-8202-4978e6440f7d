export interface dictMaps {
  label: string;
  key: string;
  options: { label: string; value: string }[];
}

// 基本信息
export interface BaseInfo {
  createBy: string;
  createAt: string;
  updateBy: string;
  updateAt: string;
}

// 认证信息
export interface AuthInfo {
  ipAddr: string;
  location: string;
  browser: string;
  browserVer: string;
  os: string;
  osVer: string;
  device: string;
}

// 返回数据
export interface FetchResp {
  code: number;
  msg: string;
  data?: any[] | string | null;
}

export interface FetchRespByPage {
  code: number;
  msg: string;
  count: number;
  list?: any[] | null;
}

export interface FetchRespImage {
  code: number;
  errno: number;
  msg: string;
  data?: any[] | null;
}
