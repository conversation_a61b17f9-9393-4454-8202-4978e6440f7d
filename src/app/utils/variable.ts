import { environment } from '@/envs/environment';

const baseUrl: string = environment.production ? '/' : '/api/';

export const API_PATHS = {
  USER: `${baseUrl}manage/users`,
  ROLE: `${baseUrl}manage/roles`,
  MENU: `${baseUrl}manage/menus`,
  DEPT: `${baseUrl}manage/depts`,
  POST: `${baseUrl}manage/posts`,
  DICT_TYPE: `${baseUrl}manage/dicttypes`,
  DICT_DATA: `${baseUrl}manage/dictdatas`,
  CONFIG: `${baseUrl}manage/configs`,
  NOTICE: `${baseUrl}manage/notices`,
  LOG_OPER: `${baseUrl}manage/logopers`,
  LOG_AUTH: `${baseUrl}manage/logauths`,
  LOG_ONLINE: `${baseUrl}manage/logonlines`,
  UPLOAD_IMG: `${baseUrl}manage/upload/img`,
  UPLOAD_FILE: `${baseUrl}manage/upload/file`,

  LOGIN: `${baseUrl}auth/login`,
  LOGOUT: `${baseUrl}auth/logout`,
  REFRESH: `${baseUrl}auth/refresh`,
  CHECKLOGIN: `${baseUrl}logincheck`,
  SERVER: `${baseUrl}server`,
};

export enum API_NAMES {
  GETALL = 'api.actions.getall',
  GETBYID = 'api.actions.getbyid',
  INSERT = 'api.actions.insert',
  UPDATE = 'api.actions.update',
  DELETE = 'api.actions.delete',
}
