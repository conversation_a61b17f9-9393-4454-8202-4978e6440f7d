import { Component, OnInit, ViewEncapsulation, booleanAttribute, inject, input } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { MenuService } from '@/services/bootstrap';
import { Breadcrumb } from '@/components/breadcrumb';

@Component({
  selector: 'app-page-header',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  host: {
    class: 'lsx-page-header',
  },
  encapsulation: ViewEncapsulation.None,
  imports: [Breadcrumb, TranslateModule],
})
export class PageHeader implements OnInit {
  private readonly router = inject(Router);
  private readonly menu = inject(MenuService);

  title = input<string | null>(null);
  subtitle = input<string | null>(null);
  nav = input<string[]>([]);
  hideBreadcrumb = input(false, { transform: booleanAttribute });

  ngOnInit() {
    const routes = this.router.url.slice(1).split('/');
    const menuLevel = this.menu.getLevel(routes);

    // TODO: It's not recommended to reassign the input signal.
    // this.title = this.title() || menuLevel[menuLevel.length - 1];
  }
}
