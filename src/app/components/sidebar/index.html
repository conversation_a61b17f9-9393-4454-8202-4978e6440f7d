@if (showHeader()) {
  <div class="lsx-sidebar-header">
    <mat-toolbar>
      <app-branding [showName]="!toggleChecked()" />
      <span class="flex-fill"></span>
      @if (showToggle()) {
        <mat-slide-toggle hideIcon
          [checked]="toggleChecked()"
          (change)="toggleCollapsed.emit()"
        />
      } @else {
        <button mat-icon-button (click)="closeSidenav.emit()">
          <mat-icon fontSet="material-symbols-rounded">close</mat-icon>
        </button>
      }
    </mat-toolbar>
  </div>
}
<div class="lsx-sidebar-main">
  @if (showUser()) {
    <app-user-panel />
  }
  <app-sidemenu [ripple]="showToggle()" />
</div>
