<form [formGroup]="form" (ngSubmit)="onSearch()">
  <formly-form [form]="form" [model]="model" [fields]="fields()" [options]="options" />
  <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="0.5rem">
    <button matButton="tonal" type="button" (click)="onReset()">
      <mat-icon fontSet="material-symbols-rounded">refresh</mat-icon>
      {{ 'operations.reset' | translate }}
    </button>
    <button matButton="filled" type="submit">
      <mat-icon fontSet="material-symbols-rounded">search</mat-icon>
      {{ 'operations.search' | translate }}
    </button>
    <a matButton="text">
      <mat-icon fontSet="material-symbols-rounded">keyboard_arrow_up</mat-icon>
      {{ 'operations.collapse' | translate }}
    </a>
    <!-- <a matButton="text">
      <mat-icon fontSet="material-symbols-rounded">keyboard_arrow_down</mat-icon>
      {{ 'operations.expand' | translate }}
    </a> -->
  </div>
</form>
