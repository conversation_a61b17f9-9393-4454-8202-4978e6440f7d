import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import { UntypedFormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormlyFieldConfig, FormlyModule, FormlyFormOptions } from '@ngx-formly/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { FlexLayoutModule } from '@ngbracket/ngx-layout';

@Component({
  selector: 'app-query-filter',
  templateUrl: './index.html',
  imports: [
    ReactiveFormsModule,
    FormlyModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    FlexLayoutModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QueryFilter {
  fields = input<FormlyFieldConfig[]>([]);
  search = output<unknown>();
  reset = output<void>();

  form = new UntypedFormGroup({});
  model = {};
  options: FormlyFormOptions = {};

  onSearch(): void {
    this.search.emit(this.model);
  }

  onReset(): void {
    this.model = {};
    this.form.reset();
    this.reset.emit();
  }
}
