import { Component, ViewEncapsulation, input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-error-code',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  encapsulation: ViewEncapsulation.None,
  imports: [
    RouterLink,
    MatButtonModule,
    TranslateModule,
  ],
})
export class ErrorCode {
  code = input.required<string>();
}
