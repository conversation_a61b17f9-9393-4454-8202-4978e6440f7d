import { Directive, OnDestroy, OnInit, effect, inject, input, model } from '@angular/core';

import { NavAccordion } from './nav-accordion';

@Directive({
  selector: '[navAccordionItem]',
  exportAs: 'navAccordionItem',
  host: {
    '[class.expanded]': 'expanded()',
  },
})
export class NavAccordionItem implements OnInit, OnDestroy {
  private readonly nav = inject(NavAccordion);

  route = input('');
  type = input<'M' | 'C' | 'L'>('C');

  expanded = model(false);

  constructor() {
    effect(() => {
      if (this.expanded()) {
        this.nav.closeOtherItems(this);
      }
    });
  }

  ngOnInit() {
    this.nav.addItem(this);
  }

  ngOnDestroy() {
    this.nav.removeItem(this);
  }

  toggle() {
    this.expanded.update(v => !v);
  }
}
