<nav aria-label="breadcrumb">
  <ol class="lsx-breadcrumb">
    @for (item of navItems; track trackByNavItem($index, item); let isFirst = $first) {
      <li class="lsx-breadcrumb-item">
        @if (isFirst) {
          <a href="#" class="link">{{ item | translate }}</a>
        }
        @if (!isFirst) {
          <mat-icon fontSet="material-symbols-rounded" class="chevron">chevron_right</mat-icon>
          <span>{{ item | translate }}</span>
        }
      </li>
    }
  </ol>
</nav>
