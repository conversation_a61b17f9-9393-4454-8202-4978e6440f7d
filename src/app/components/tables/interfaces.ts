import { TemplateRef } from '@angular/core';
import { MatBadgePosition, MatBadgeSize } from '@angular/material/badge';
import { MatButtonAppearance } from '@angular/material/button';
import { ThemePalette } from '@angular/material/core';
import { SortDirection } from '@angular/material/sort';
import { TooltipPosition, TooltipTouchGestures } from '@angular/material/tooltip';
import { Observable } from 'rxjs';

/** Column definition of grid. */
export interface LsxTableColumn<T = any> {
  field: string;
  header?: string | Observable<string>;
  hide?: boolean;
  show?: boolean;
  disabled?: boolean;
  pinned?: LsxTableColumnPinValue;
  left?: string;
  right?: string;
  width?: string;
  resizable?: boolean;
  minWidth?: number;
  maxWidth?: number;
  sortable?: boolean | string;
  sortProp?: LsxTableSortProp;
  type?: LsxTableColumnType;
  typeParameter?: LsxTableColumnTypeParameter;
  tag?: LsxTableColumnTag;
  buttons?: LsxTableColumnButton<T>[] | ((rowData: T) => LsxTableColumnButton<T>[]);
  formatter?: (rowData: T, colDef?: LsxTableColumn) => any;
  cellTemplate?: TemplateRef<any> | null;
  showExpand?: boolean;
  description?: string;
  summary?: ((data: T[], colDef?: LsxTableColumn) => any) | string;
  class?: string | ((rowData?: T, colDef?: LsxTableColumn) => string);
}

/** Possible column pin values.  */
export declare type LsxTableColumnPinValue = 'left' | 'right' | null;

/** Column pin option  */
export interface LsxTableColumnPinOption {
  label: string | Observable<string>;
  value: LsxTableColumnPinValue;
}

/** Possible column type values. */
export declare type LsxTableColumnType =
  | 'button'
  | 'tag'
  | 'switch'
  | 'link'
  | 'image'
  | 'boolean'
  | 'number'
  | 'currency'
  | 'percent'
  | 'date';

/** Column type parameter. */
export interface LsxTableColumnTypeParameter {
  currencyCode?: string;
  display?: string | boolean;
  digitsInfo?: string;
  format?: string;
  locale?: string;
  timezone?: string;
}

/** The properties of data sort. */
export interface LsxTableSortProp {
  arrowPosition?: 'before' | 'after';
  disableClear?: boolean;
  id?: string;
  start?: 'asc' | 'desc';
}

/** Column tag of grid. */
export interface LsxTableColumnTag {
  [key: number]: LsxTableColumnTagValue;
  [key: string]: LsxTableColumnTagValue;
}

/** The properties of column tag. */
export interface LsxTableColumnTagValue {
  text?: string;
  color?: string;
}

/** The properties of column button. */
export interface LsxTableColumnButton<T = any> {
  type?: LsxTableButtonType;
  text?: string | Observable<string>;
  icon?: string;
  fontIcon?: string;
  svgIcon?: string;
  color?: ThemePalette;
  class?: string;
  disabled?: boolean | ((rowData: T) => boolean);
  click?: (rowData: T) => void;
  iif?: (rowData: T) => boolean;
  pop?: string | Observable<string> | LsxTableColumnButtonPop;
  tooltip?: string | Observable<string> | LsxTableColumnButtonTooltip;
  badge?: number | string | Observable<string> | LsxTableColumnButtonBadge;
  children?: LsxTableMenuItem<T>[];
}

/** The properties of column button pop. */
export interface LsxTableColumnButtonPop {
  title: string | Observable<string>;
  description?: string | Observable<string>;
  okColor?: ThemePalette;
  okText?: string | Observable<string>;
  closeColor?: ThemePalette;
  closeText?: string | Observable<string>;
}

/** The properties of column button tooltip. */
export interface LsxTableColumnButtonTooltip {
  message: string | Observable<string>;
  position?: TooltipPosition;
  positionAtOrigin?: boolean;
  class?: any;
  hideDelay?: number;
  showDelay?: number;
  touchGestures?: TooltipTouchGestures;
  disabled?: boolean;
}

/** The properties of column button badge. */
export interface LsxTableColumnButtonBadge {
  content: number | string | Observable<string>;
  description?: string | Observable<string>;
  color?: ThemePalette;
  position?: MatBadgePosition;
  size?: MatBadgeSize;
  overlap?: boolean;
  disabled?: boolean;
  hidden?: boolean;
}

/** Cell template. */
export interface LsxTableCellTemplate {
  [key: string]: TemplateRef<any>;
}

/** Row selection formatter. */
export interface LsxTableRowSelectionFormatter<T = any> {
  disabled?: (rowData: T, index: number) => boolean;
  hideCheckbox?: (rowData: T, index: number) => boolean;
}

/** Row class formatter. */
export interface LsxTableRowClassFormatter<T = any> {
  [className: string]: (rowData: T, index: number) => boolean;
}

/** Possible button type values. */
export type LsxTableButtonType = MatButtonAppearance | 'icon';

/**
 * Represents the default options for the grid that can be configured
 * using the `MTX_GRID_DEFAULT_OPTIONS` injection token.
 */
export interface LsxTableDefaultOptions {
  columnResizable?: boolean;
  emptyValuePlaceholder?: string;

  pageOnFront?: boolean;
  showPaginator?: boolean;
  pageDisabled?: boolean;
  showFirstLastButtons?: boolean;
  pageIndex?: number;
  pageSize?: number;
  pageSizeOptions?: number[];
  hidePageSize?: boolean;

  sortOnFront?: boolean;
  sortActive?: string;
  sortDirection?: SortDirection;
  sortDisableClear?: boolean;
  sortDisabled?: boolean;
  sortStart?: 'asc' | 'desc';

  rowHover?: boolean;
  rowStriped?: boolean;

  multiSelectable?: boolean;
  multiSelectionWithClick?: boolean;
  rowSelectable?: boolean;
  hideRowSelectionCheckbox?: boolean;
  disableRowClickSelection?: boolean;

  cellSelectable?: boolean;

  showToolbar?: boolean;
  toolbarTitle?: string;

  columnHideable?: boolean;
  columnHideableChecked?: 'show' | 'hide';
  columnSortable?: boolean;
  columnPinnable?: boolean;
  columnPinOptions?: LsxTableColumnPinOption[];

  showColumnMenuButton?: boolean;
  columnMenuButtonText?: string;
  columnMenuButtonType?: LsxTableButtonType;
  columnMenuButtonColor?: ThemePalette;
  columnMenuButtonClass?: string;
  columnMenuButtonIcon?: string;
  columnMenuButtonFontIcon?: string;
  columnMenuButtonSvgIcon?: string;

  showColumnMenuHeader?: boolean;
  columnMenuHeaderText?: string;
  showColumnMenuFooter?: boolean;
  columnMenuFooterText?: string;

  noResultText?: string;
}

/** The properties of menu item. */
export interface LsxTableMenuItem<T = any> {
  text: string | Observable<string>;
  icon?: string;
  fontIcon?: string;
  svgIcon?: string;
  class?: string;
  disabled?: boolean | ((rowData: T) => boolean);
  click?: (rowData: T) => void;
  iif?: (rowData: T) => boolean;
  children?: LsxTableMenuItem[];
}
