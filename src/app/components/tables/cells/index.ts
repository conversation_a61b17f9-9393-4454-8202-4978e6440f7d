import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { C<PERSON><PERSON>cyPipe, PercentPipe, DatePipe } from '@angular/common';

@Component({
  selector: 'app-table-cell',
  templateUrl: './index.html',
  styleUrls: ['./index.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CurrencyPipe,
    PercentPipe,
    DatePipe,
  ],
})
export class TableCellComponent {
  cellType = input.required<
    | 'button'
    | 'tag'
    | 'currency'
    | 'number'
    | 'percent'
    | 'image'
    | 'link'
    | 'switch'
    | 'boolean'
    | 'date'
  >();
  value = input<any>();
}
