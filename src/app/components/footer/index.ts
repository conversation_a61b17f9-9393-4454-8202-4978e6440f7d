import { Component, OnInit, computed, input, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { FlexLayoutModule } from '@ngbracket/ngx-layout';

@Component({
  selector: 'app-footer',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  imports: [
    MatButtonModule,
    TranslateModule,
    FlexLayoutModule,
  ]
})
export class FooterComponent implements OnInit {
  isMobile = input(false);

  appName = computed(() => (this.isMobile() ? 'app.shortname' : 'app.fullname'));

  createYear = '2025';
  currentYear = signal<string>(new Date().getFullYear() + '');

  ngOnInit(): void {
    if (this.currentYear() !== this.createYear) {
      this.currentYear.set(`${this.createYear} - ${this.currentYear()}`);
    }
  }
}
