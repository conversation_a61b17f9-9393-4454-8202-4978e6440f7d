import { Component, OnInit, ViewEncapsulation, inject, input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterLink } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { FlexLayoutModule } from '@ngbracket/ngx-layout';

import { AuthService, User, unknown } from '@/services/authentication';

import { UserAvatar } from '../user-avatar';

@Component({
  selector: 'app-user-panel',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  encapsulation: ViewEncapsulation.None,
  imports: [
    RouterLink,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule,
    FlexLayoutModule,
    UserAvatar,
  ],
})
export class UserPanel implements OnInit {
  private readonly auth = inject(AuthService);

  showName = input(true);

  user!: User;

  ngOnInit(): void {
    // this.auth.user().subscribe(user => this.user = user);
    this.user = unknown;
  }
}
