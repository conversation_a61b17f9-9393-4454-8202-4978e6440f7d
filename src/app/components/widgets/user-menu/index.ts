import { ChangeDetectorRef, Component, OnInit, inject } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { debounceTime, tap } from 'rxjs';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { FlexLayoutModule } from '@ngbracket/ngx-layout';

import { AuthService, SettingsService, User, unknown } from '@/services';
import { UserAvatar } from '../user-avatar';

@Component({
  selector: 'app-user',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  imports: [
    MatButtonModule,
    MatDividerModule,
    MatIconModule,
    MatMenuModule,
    TranslateModule,
    FlexLayoutModule,
    UserAvatar,
  ],
})
export class UserMenu implements OnInit {
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly auth = inject(AuthService);
  private readonly router = inject(Router);
  private readonly settings = inject(SettingsService);

  user: User = unknown;

  ngOnInit(): void {
    // this.auth
    //   .user()
    //   .pipe(
    //     tap(user => (this.user = user)),
    //     debounceTime(10)
    //   )
    //   .subscribe(() => this.cdr.detectChanges());
  }

  logout() {
    this.auth.logout().subscribe(() => {
      this.router.navigateByUrl('/auth/login');
    });
  }

  restore() {
    this.settings.reset();
    window.location.reload();
  }
}
