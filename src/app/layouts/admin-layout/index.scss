@use '@angular/material' as mat;
@use '../../../styles/grid/breakpoints';

.lsx-container-wrap,
.lsx-container {
  --mat-sidenav-content-background-color: transparent;

  height: 100%;
}

.lsx-sidenav {
  --mat-sidenav-container-width: var(--sidenav-width);
  position: absolute;
  overflow-x: hidden;
  border-width: 0 !important;
  transition-property: transform, width !important;
}

// Layout control
.lsx-header-above {
  .lsx-container {
    height: calc(100% - var(--mat-toolbar-standard-height)) !important;

    @include breakpoints.bp-lt(small) {
      & {
        height: calc(100% - var(--mat-toolbar-mobile-height)) !important;
      }
    }
  }

  .lsx-sidebar-main {
    height: 100% !important;
    padding-top: 0.5rem;
  }
}

// Layout control
.lsx-sidenav-collapsed,
.lsx-sidenav-collapsed-fix {
  .lsx-sidenav {
    width: var(--sidenav-collapsed-width);

    .menu-name,
    .menu-label,
    .menu-badge,
    .menu-caret,
    .lsx-user-panel-info {
      opacity: 0;
    }

    .menu-icon.submenu-icon {
      opacity: 1;
    }

    .lsx-user-panel-avatar {
      transform: scale(0.5);
    }

    &:hover {
      width: var(--sidenav-width);

      .menu-name,
      .menu-label,
      .menu-badge,
      .menu-caret,
      .lsx-user-panel-info {
        opacity: 1;
      }

      .menu-icon.submenu-icon {
        opacity: 0;
      }

      .lsx-user-panel-avatar {
        transform: scale(1);
      }
    }
  }
}

// Layout control
.lsx-sidenav-collapsed {
  .lsx-content {
    margin-left: var(--sidenav-collapsed-width) !important;

    [dir='rtl'] & {
      margin-right: var(--sidenav-collapsed-width) !important;
      margin-left: auto !important;
    }
  }

  &[dir='rtl'] .lsx-content {
    margin-right: var(--sidenav-collapsed-width) !important;
    margin-left: auto !important;
  }
}

// Layout control
.lsx-navbar-top {
  .lsx-topmenu {
    top: 0;
  }

  .lsx-branding {
    margin-left: 1rem;

    [dir='rtl'] & {
      margin-right: 1rem;
      margin-left: auto;
    }
  }
}

// Layout control
.lsx-header-fixed {
  .lsx-header {
    position: sticky;
    top: 0;
  }

  .lsx-topmenu {
    top: var(--mat-toolbar-standard-height);

    @include breakpoints.bp-lt(small) {
      & {
        top: var(--mat-toolbar-mobile-height);
      }
    }
  }
}

// Fix the init content width
.lsx-content-width-fix {
  .lsx-content {
    margin-left: var(--sidenav-width) !important;

    [dir='rtl'] & {
      margin-right: var(--sidenav-width) !important;
      margin-left: auto !important;
    }
  }
}

.lsx-content {
  &.mat-drawer-content {
    display: flex;
  }
  flex-direction: column;
}

.lsx-page-content {
  flex: 1 1 auto;
  overflow: auto;
  position: relative;
  padding: var(--gutter);

  .lsx-header+&,
  .lsx-header-above &,
  .lsx-navbar-top & {
    padding-top: 0;
  }
}
