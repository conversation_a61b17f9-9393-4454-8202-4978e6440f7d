<div
  class="lsx-container-wrap"
  [class.lsx-sidenav-collapsed]="options.sidenavCollapsed && options.navPos !== 'top'"
  [class.lsx-navbar-side]="options.navPos === 'side'"
  [class.lsx-navbar-top]="options.navPos === 'top'"
  [class.lsx-header-above]="options.headerPos === 'above'"
  [class.lsx-header-fixed]="options.headerPos === 'fixed'"
  [dir]="options.dir"
>
  <ng-progress ngProgressRouter />
  <!-- Header Above -->
  @if (options.showHeader && options.headerPos === 'above') {
    <app-header
      (toggleSidenav)="sidenav.toggle()"
      (toggleSidenavNotice)="sidenavNotice.toggle()"
      [showBranding]="true"
    />
  }
  <mat-sidenav-container class="lsx-container" autosize autoFocus>
    <!-- Sidenav -->
    <mat-sidenav #sidenav
      class="lsx-sidenav"
      [mode]="isOver ? 'over' : 'side'"
      [opened]="options.navPos === 'side' && options.sidenavOpened && !isOver"
      (openedChange)="onSidenavOpenedChange($event)"
      (closedStart)="onSidenavClosedStart()"
    >
      <app-sidebar
        [showToggle]="!isOver"
        [showUser]="options.showUserPanel"
        [showHeader]="options.headerPos !== 'above'"
        [toggleChecked]="options.sidenavCollapsed"
        (toggleCollapsed)="toggleCollapsed()"
        (closeSidenav)="sidenav.close()"
      />
    </mat-sidenav>
    <!-- Sidenav Notice -->
    <mat-sidenav #sidenavNotice position="end" mode="over">
      <app-sidebar-notice />
    </mat-sidenav>
    <mat-sidenav-content #content class="lsx-content">
      <!-- Header -->
      @if (options.showHeader && options.headerPos !== 'above') {
        <app-header
          [showToggle]="!options.sidenavCollapsed && options.navPos !== 'top'"
          [showBranding]="options.navPos === 'top'"
          (toggleSidenav)="sidenav.toggle()"
          (toggleSidenavNotice)="sidenavNotice.toggle()"
        />
      }
      <!-- @if (options.navPos === 'top') {
        <app-topmenu />
      } -->
      <main class="lsx-page-content">
        <router-outlet />
      </main>
      <app-footer [isMobile]="isOver" />
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>
