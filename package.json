{"name": "ls-manage", "version": "0.0.1", "author": "Zevthyr(<EMAIL>)", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "build:release": "ng build --configuration production --base-href=/ls/", "watch": "ng build --watch --configuration development", "hmr": "ng serve --hmr --host 0.0.0.0 --configuration development", "test": "ng test"}, "prettier": {"printWidth": 100, "singleQuote": true, "overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/animations": "~20.3.1", "@angular/cdk": "~20.2.4", "@angular/common": "~20.3.1", "@angular/compiler": "~20.3.1", "@angular/core": "~20.3.1", "@angular/forms": "~20.3.1", "@angular/material": "~20.2.4", "@angular/material-date-fns-adapter": "~20.2.4", "@angular/platform-browser": "~20.3.1", "@angular/router": "~20.3.1", "@ngbracket/ngx-layout": "^20.0.1", "@ngx-formly/core": "^7.0.0", "@ngx-formly/material": "^7.0.0", "@ngx-translate/core": "^17.0.0", "@ngx-translate/http-loader": "^17.0.0", "@ngxs/store": "^20.1.0", "date-fns": "^4.1.0", "jdenticon": "3.3.0", "material-symbols": "^0.35.2", "ngx-jdenticon": "2.0.0", "ngx-permissions": "^19.0.0", "ngx-progressbar": "^14.0.0", "ngx-scrollbar": "^18.0.0", "ngx-toastr": "^19.1.0", "rxjs": "~7.8.2", "screenfull": "^6.0.2", "tslib": "^2.8.1", "zone.js": "~0.15.1"}, "devDependencies": {"@angular/build": "^20.3.2", "@angular/cli": "^20.3.2", "@angular/compiler-cli": "^20.3.1", "@types/jasmine": "~5.1.9", "jasmine-core": "~5.10.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.9.2"}, "packageManager": "pnpm@10.17.0"}